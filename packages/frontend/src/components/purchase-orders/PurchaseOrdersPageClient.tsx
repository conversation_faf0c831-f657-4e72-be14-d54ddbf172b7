'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/purchase-orders/data-table';
import { createPurchaseOrderColumns } from '@/components/purchase-orders/columns';
import { PurchaseOrderQuickView } from '@/components/purchase-orders/purchase-order-quick-view';
import { SendToSupplierDialog } from '@/components/purchase-orders/send-to-supplier-dialog';
import { PurchaseOrder, PurchaseOrderQueryParams, PurchaseOrderWithRelations } from '@/types/purchase-order';
import {
  usePurchaseOrders,
  usePurchaseOrder,
  usePurchaseOrdersInvalidate,
  useCancelPurchaseOrder,
  useSendPurchaseOrder,
  useDeletePurchaseOrder,
  useExportPurchaseOrders,
  useDownloadPurchaseOrderPdf,
} from '@/hooks/usePurchaseOrders';
import { useSuppliers } from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { DEFAULT_PURCHASE_ORDER_PAGINATION } from '@/lib/constants/purchase-order';

interface PurchaseOrdersPageClientProps {
  initialQuery: PurchaseOrderQueryParams;
}

export function PurchaseOrdersPageClient({
  initialQuery,
}: PurchaseOrdersPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<PurchaseOrderQueryParams>(initialQuery);
  const [selectedPurchaseOrderId, setSelectedPurchaseOrderId] = useState<string | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);
  const [showSendToSupplierDialog, setShowSendToSupplierDialog] = useState(false);
  // Track the navigation context for send to supplier modal
  const [sendToSupplierContext, setSendToSupplierContext] = useState<'table' | 'quickview' | null>(null);

  // Use TanStack Query for data fetching
  const { data, isLoading, refetch } = usePurchaseOrders(query);
  const { data: selectedPurchaseOrder } = usePurchaseOrder(selectedPurchaseOrderId || '');

  // Use invalidation hook for stats refresh
  const { invalidateAll } = usePurchaseOrdersInvalidate();

  // Use mutation hooks for purchase order operations
  const cancelPurchaseOrderMutation = useCancelPurchaseOrder();
  const sendPurchaseOrderMutation = useSendPurchaseOrder();
  const deletePurchaseOrderMutation = useDeletePurchaseOrder();
  const exportPurchaseOrdersMutation = useExportPurchaseOrders();
  const downloadPurchaseOrderPdfMutation = useDownloadPurchaseOrderPdf();

  // Get suppliers for filter options
  const { data: suppliersResponse } = useSuppliers({ 
    page: 1, 
    limit: 100, 
    sortBy: 'name', 
    sortOrder: 'asc' 
  });

  const supplierOptions = suppliersResponse?.data?.map(supplier => ({
    value: supplier.id,
    label: `${supplier.name} (${supplier.code})`,
  })) || [];

  // Filter state
  const [filters, setFilters] = useState<PurchaseOrderQueryParams>({
    status: query.status,
    supplierId: query.supplierId,
    orderDateFrom: query.orderDateFrom,
    orderDateTo: query.orderDateTo,
    expectedDeliveryFrom: query.expectedDeliveryFrom,
    expectedDeliveryTo: query.expectedDeliveryTo,
  });

  const handleQueryChange = useCallback((newQuery: PurchaseOrderQueryParams) => {
    setQuery(newQuery);
  }, []);

  const handleFilterChange = useCallback((key: keyof PurchaseOrderQueryParams, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Apply filters to query
    setQuery(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filtering
    }));
  }, [filters]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      status: undefined,
      supplierId: undefined,
      orderDateFrom: undefined,
      orderDateTo: undefined,
      expectedDeliveryFrom: undefined,
      expectedDeliveryTo: undefined,
    };
    setFilters(clearedFilters);
    setQuery(prev => ({
      ...prev,
      ...clearedFilters,
      page: 1,
    }));
  }, []);

  const handleViewPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setShowQuickView(true);
  }, []);

  const handleEditPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}/edit`);
  }, [router]);

  const handleCreatePurchaseOrder = useCallback(() => {
    router.push('/dashboard/purchase-orders/new');
  }, [router]);



  const handleCancelPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder, reason: string) => {
    try {
      await cancelPurchaseOrderMutation.mutateAsync({
        id: purchaseOrder.id,
        reason
      });
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        // Refresh the selected purchase order
        refetch();
      }
    } catch (error) {
      console.error('Failed to cancel purchase order:', error);
    }
  }, [cancelPurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId, refetch]);

  // Handler for "Kirim ke supplier" from table action dropdown
  const handleSendPurchaseOrderFromTable = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setSendToSupplierContext('table');
    setShowSendToSupplierDialog(true);
    // Ensure quick view is closed if it was open
    setShowQuickView(false);
  }, []);

  // Handler for "Kirim ke supplier" from quick view dialog
  const handleSendPurchaseOrderFromQuickView = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setSendToSupplierContext('quickview');
    setShowQuickView(false); // Close quick view first
    setShowSendToSupplierDialog(true);
  }, []);

  const handleDeletePurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await deletePurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        setShowQuickView(false);
        setSelectedPurchaseOrderId(null);
      }
    } catch (error) {
      console.error('Failed to delete purchase order:', error);
    }
  }, [deletePurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId]);

  const handlePrintPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrder.id);
    } catch (error) {
      console.error('Failed to print purchase order:', error);
    }
  }, [downloadPurchaseOrderPdfMutation]);

  const handleDownloadPdf = useCallback(async (purchaseOrder: PurchaseOrderWithRelations) => {
    await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrder.id);
  }, [downloadPurchaseOrderPdfMutation]);

  const handleActualSendToSupplier = useCallback(async (purchaseOrder: PurchaseOrderWithRelations) => {
    try {
      await sendPurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        refetch();
      }
    } catch (error) {
      console.error('Failed to send purchase order:', error);
    }
  }, [sendPurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId, refetch]);

  const handleExport = useCallback(async () => {
    try {
      await exportPurchaseOrdersMutation.mutateAsync({
        ...query,
        format: 'xlsx',
      });
    } catch (error) {
      console.error('Failed to export purchase orders:', error);
    }
  }, [exportPurchaseOrdersMutation, query]);

  const handleRefresh = useCallback(() => {
    refetch();
    invalidateAll();
  }, [refetch, invalidateAll]);

  const handleViewDetails = useCallback((purchaseOrder: PurchaseOrder) => {
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}`);
  }, [router]);

  // Create columns with action handlers
  const columns = createPurchaseOrderColumns({
    onView: handleViewPurchaseOrder,
    onEdit: handleEditPurchaseOrder,
    onDelete: handleDeletePurchaseOrder,
    onCancel: (po) => handleCancelPurchaseOrder(po, 'Dibatalkan dari tabel'),
    onSend: handleSendPurchaseOrderFromTable, // Use table-specific handler
    onPrint: handlePrintPurchaseOrder,
  });



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Purchase Orders</h1>
          <p className="text-muted-foreground">
            Kelola purchase order dan pemesanan ke supplier
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={exportPurchaseOrdersMutation.isPending}
          >
            <Download className="h-4 w-4 mr-2" />
            {exportPurchaseOrdersMutation.isPending ? 'Mengekspor...' : 'Ekspor'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleCreatePurchaseOrder}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Purchase Order
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full">
            <DataTable
              columns={columns}
              data={data?.data || []}
              meta={data?.meta || { 
                total: 0, 
                page: 1, 
                limit: 10, 
                totalPages: 0, 
                hasNextPage: false, 
                hasPreviousPage: false 
              }}
              query={query}
              onQueryChange={handleQueryChange}
              loading={isLoading}
              searchPlaceholder="Cari purchase order..."
              onRowClick={handleViewPurchaseOrder}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              filterOptions={{
                suppliers: supplierOptions,
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick View Modal */}
      <PurchaseOrderQuickView
        purchaseOrder={selectedPurchaseOrder || null}
        open={showQuickView}
        onOpenChange={(open) => {
          setShowQuickView(open);
          if (!open) {
            setSelectedPurchaseOrderId(null);
          }
        }}
        onEdit={handleEditPurchaseOrder}
        onCancel={handleCancelPurchaseOrder}
        onSend={handleSendPurchaseOrderFromQuickView} // Use quick view-specific handler
        onPrint={handlePrintPurchaseOrder}
        onViewDetails={handleViewDetails}
      />

      {/* Send to Supplier Dialog */}
      <SendToSupplierDialog
        open={showSendToSupplierDialog}
        onOpenChange={(open) => {
          setShowSendToSupplierDialog(open);
          if (!open) {
            // Handle different navigation flows based on context
            if (sendToSupplierContext === 'quickview') {
              // Return to quick view if came from quick view
              setShowQuickView(true);
            } else {
              // Clear selection if came from table
              setSelectedPurchaseOrderId(null);
            }
            setSendToSupplierContext(null);
          }
        }}
        purchaseOrder={selectedPurchaseOrder || null}
        onDownloadPdf={handleDownloadPdf}
        onSendToSupplier={handleActualSendToSupplier}
        isDownloading={downloadPurchaseOrderPdfMutation.isPending}
        isSending={sendPurchaseOrderMutation.isPending}
      />
    </div>
  );
}
